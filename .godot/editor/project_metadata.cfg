[editor_metadata]

executable_path="/Users/<USER>/Desktop/Godot 2.app/Contents/MacOS/Godot"

[recent_files]

scenes=["res://scenes/Chapter6.tscn", "res://scenes/Chapter2.tscn", "res://scenes/MainMenu.tscn", "res://scenes/Chapter7.tscn", "res://scenes/Chapter5.tscn", "res://scenes/Chapter4.tscn", "res://scenes/Chapter3.tscn", "res://scenes/CaesarCipherPuzzle.tscn"]
scripts=["res://scripts/MainMenu.gd", "res://scripts/font_loader.gd", "res://scripts/MemoryTestPuzzle.gd", "res://scripts/DialogueSystem.gd", "res://scripts/CaesarCipherPuzzle.gd", "res://README.md", "res://scripts/Chapter.gd"]

[uid_upgrade_tool]

run_on_restart=false
resave_paths=PackedStringArray()

[dialog_bounds]

project_settings=Rect2(528, 418, 2400, 1400)
export=Rect2(800, 540, 1800, 1200)

[quick_open_dialog]

last_mode=1
