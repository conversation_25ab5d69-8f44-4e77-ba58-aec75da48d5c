extends Control

@onready var new_game_button: Button = $CenterContainer/VBoxContainer/MenuButtons/NewGameButton
@onready var chapters_button: Button = $CenterContainer/VBoxContainer/MenuButtons/ChaptersButton
@onready var settings_button: Button = $CenterContainer/VBoxContainer/MenuButtons/SettingsButton
@onready var about_button: Button = $CenterContainer/VBoxContainer/MenuButtons/AboutButton
@onready var quit_button: Button = $CenterContainer/VBoxContainer/MenuButtons/QuitButton

func _ready():
	# Pripojenie sign<PERSON>lov tlačidiel
	new_game_button.pressed.connect(_on_new_game_pressed)
	chapters_button.pressed.connect(_on_chapters_pressed)
	settings_button.pressed.connect(_on_settings_pressed)
	about_button.pressed.connect(_on_about_pressed)
	quit_button.pressed.connect(_on_quit_pressed)

	# Nastavenie fokusu na prvé tlačidlo
	new_game_button.grab_focus()

	# Aplikovanie nastavení
	GameManager.apply_settings()

	# Spustenie main menu hudby
	AudioManager.play_music("main_menu")

func _on_new_game_pressed():
	# Začať novú hru - ísť na prvú kapitolu
	GameManager.go_to_chapter(1)

func _on_chapters_pressed():
	# Otvoriť menu kapitol
	GameManager.go_to_chapters()

func _on_settings_pressed():
	# Otvoriť audio nastavenia
	get_tree().change_scene_to_file("res://scenes/AudioSettings.tscn")

func _on_about_pressed():
	# Otvoriť informácie o hre
	GameManager.go_to_about()

func _on_quit_pressed():
	# Ukončiť hru
	get_tree().quit()

func _input(event):
	# Navigácia klávesnicou a dotykom
	if event.is_action_pressed("ui_cancel"):
		_on_quit_pressed()

	# Dotykové ovládanie pre Android back button
	if event is InputEventKey and event.keycode == KEY_BACK:
		_on_quit_pressed()
