extends Control

# Button references
@onready var nova_hra_button = $MenuContainer/ButtonContainer/NovaHraButton
@onready var kapitoly_button = $MenuContainer/ButtonContainer/KapitolyButton
@onready var nastavenia_button = $MenuContainer/ButtonContainer/NastaveniaButton
@onready var o_hre_button = $MenuContainer/ButtonContainer/OHreButton

# Background and logo
@onready var background = $Background
@onready var logo = $MenuContainer/LogoContainer/Logo

func _ready():
	setup_background()
	setup_logo()
	setup_buttons()
	setup_audio()
	adapt_to_screen_size()

func setup_background():
	# Nastav pozadie
	background.texture = load("res://assets/MENU.png")
	background.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_COVERED
	background.anchor_left = 0
	background.anchor_top = 0
	background.anchor_right = 1
	background.anchor_bottom = 1

func setup_logo():
	# Nastav logo
	logo.texture = load("res://assets/logo.png")
	logo.stretch_mode = TextureRect.STRETCH_KEEP_ASPECT_CENTERED
	logo.custom_minimum_size = Vector2(400, 150)

func setup_buttons():
	# Nastav texty buttonov
	nova_hra_button.text = "NOVÁ HRA"
	kapitoly_button.text = "KAPITOLY"
	nastavenia_button.text = "NASTAVENIA"
	o_hre_button.text = "O HRE"

	# Aplikuj rámček na všetky buttony
	var ramcek_texture = load("res://assets/RAMCEK.png")

	for button in [nova_hra_button, kapitoly_button, nastavenia_button, o_hre_button]:
		setup_button_style(button, ramcek_texture)

	# Pripoj signály
	nova_hra_button.pressed.connect(_on_nova_hra_pressed)
	kapitoly_button.pressed.connect(_on_kapitoly_pressed)
	nastavenia_button.pressed.connect(_on_nastavenia_pressed)
	o_hre_button.pressed.connect(_on_o_hre_pressed)

	# Nastaviť fokus na prvé tlačidlo
	nova_hra_button.grab_focus()

func setup_button_style(button: Button, ramcek_texture: Texture2D):
	# Vytvor StyleBoxTexture pre rámček
	var style_normal = StyleBoxTexture.new()
	style_normal.texture = ramcek_texture
	style_normal.texture_margin_left = 16
	style_normal.texture_margin_top = 16
	style_normal.texture_margin_right = 16
	style_normal.texture_margin_bottom = 16

	var style_hover = StyleBoxTexture.new()
	style_hover.texture = ramcek_texture
	style_hover.texture_margin_left = 16
	style_hover.texture_margin_top = 16
	style_hover.texture_margin_right = 16
	style_hover.texture_margin_bottom = 16
	style_hover.modulate_color = Color(1.2, 1.2, 1.2)

	var style_pressed = StyleBoxTexture.new()
	style_pressed.texture = ramcek_texture
	style_pressed.texture_margin_left = 16
	style_pressed.texture_margin_top = 16
	style_pressed.texture_margin_right = 16
	style_pressed.texture_margin_bottom = 16
	style_pressed.modulate_color = Color(0.8, 0.8, 0.8)

	# Aplikuj štýly na button
	button.add_theme_stylebox_override("normal", style_normal)
	button.add_theme_stylebox_override("hover", style_hover)
	button.add_theme_stylebox_override("pressed", style_pressed)

	# Nastav veľkosť buttonu
	button.custom_minimum_size = Vector2(300, 60)

	# Aplikuj font
	FontLoader.apply_ui_font(button)
	button.add_theme_font_size_override("font_size", 20)

	# Centruj text
	button.alignment = HORIZONTAL_ALIGNMENT_CENTER

func setup_audio():
	# Spusti main menu hudbu
	if AudioManager:
		AudioManager.play_music("main_menu")

func adapt_to_screen_size():
	var screen_size = get_viewport().get_visible_rect().size

	if screen_size.x < 600:  # Small phone
		logo.custom_minimum_size = Vector2(300, 100)
		for button in get_buttons():
			button.custom_minimum_size = Vector2(250, 50)
			button.add_theme_font_size_override("font_size", 16)

	elif screen_size.x > 1200:  # Large screen
		logo.custom_minimum_size = Vector2(500, 200)
		for button in get_buttons():
			button.custom_minimum_size = Vector2(400, 80)
			button.add_theme_font_size_override("font_size", 24)

func get_buttons() -> Array:
	return [nova_hra_button, kapitoly_button, nastavenia_button, o_hre_button]

# Button callbacks
func _on_nova_hra_pressed():
	print("Starting new game...")
	get_tree().change_scene_to_file("res://scenes/Chapter1.tscn")

func _on_kapitoly_pressed():
	print("Opening chapter selection...")
	get_tree().change_scene_to_file("res://scenes/ChaptersMenu.tscn")

func _on_nastavenia_pressed():
	print("Opening settings...")
	get_tree().change_scene_to_file("res://scenes/AudioSettings.tscn")

func _on_o_hre_pressed():
	print("Opening about...")
	get_tree().change_scene_to_file("res://scenes/AboutGame.tscn")
