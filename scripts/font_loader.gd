extends Node
class_name FontLoader

# Gotické fonty pre Prekliate Dedičstvo
# Načítava Google Fonts s fallback možnosťami

# TTF Font paths - priame cesty k lokálnym fontom
const FONT_PATHS = {
	"cinzel_regular": "res://fonts/C<PERSON><PERSON>,Corm<PERSON><PERSON>_<PERSON>,Linden_Hill/Cinzel/static/Cinzel-Regular.ttf",
	"cinzel_medium": "res://fonts/Cin<PERSON>,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Medium.ttf",
	"cinzel_bold": "res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cinzel/static/Cinzel-Bold.ttf",
	"cormorant_regular": "res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cormorant_Garamond/static/CormorantGaramond-Regular.ttf",
	"cormorant_italic": "res://fonts/Cinzel,Cormoran<PERSON>_<PERSON>,Linden_Hill/Cormorant_Garamond/static/CormorantGaramond-Italic.ttf",
	"cormorant_medium": "res://fonts/Cinzel,Cormorant_Garamond,Linden_Hill/Cormorant_Garamond/static/CormorantGaramond-Medium.ttf"
}

# Fallback fonts
const FALLBACK_FONTS = {
	"gothic_serif": ["Times New Roman", "Book Antiqua", "serif"],
	"readable_serif": ["Georgia", "Times New Roman", "serif"],
	"clean_sans": ["Arial", "Helvetica", "sans-serif"],
	"decorative": ["Papyrus", "Bradley Hand", "fantasy"]
}

# Font configurations - podľa vášho návodu
const FONT_CONFIGS = {
	"chapter_title": {
		"font_path": "cinzel_regular",
		"size": 28,
		"color": Color(0.831, 0.686, 0.216), # #D4AF37 zlatá
		"weight": "regular",
		"shadow": true,
		"fallback": "gothic_serif"
	},
	"menu_buttons": {
		"font_path": "cinzel_regular",
		"size": 20,
		"color": Color.WHITE,
		"weight": "regular",
		"shadow": false,
		"fallback": "gothic_serif"
	},
	"character_names": {
		"font_path": "cinzel_regular",
		"size": 18,
		"color": Color(0.831, 0.686, 0.216), # #D4AF37 zlatá
		"weight": "regular",
		"shadow": false,
		"fallback": "gothic_serif"
	},
	"character_dialogue": {
		"font_path": "cormorant_regular",
		"size": 18,
		"color": Color(0.961, 0.961, 0.863), # #F5F5DC krémová
		"weight": "regular",
		"shadow": false,
		"fallback": "readable_serif"
	},
	"narrator_text": {
		"font_path": "cormorant_italic", # Použijem Cormorant italic namiesto Crimson
		"size": 16,
		"color": Color(0.878, 0.878, 0.878), # #E0E0E0 svetlá sivá
		"weight": "regular",
		"italic": true,
		"shadow": false,
		"fallback": "readable_serif"
	},
	"ui_elements": {
		"font_path": "cinzel_regular", # Fallback na Cinzel namiesto Montserrat
		"size": 16,
		"color": Color.WHITE,
		"weight": "regular",
		"shadow": false,
		"fallback": "clean_sans"
	},
	"puzzle_text": {
		"font_path": "cinzel_medium", # Použijem Cinzel Medium pre puzzle
		"size": 20,
		"color": Color(0.545, 0.271, 0.075), # #8B4513 červenohnedá
		"weight": "medium",
		"shadow": true,
		"fallback": "gothic_serif"
	}
}

# Responzívne škálovanie
const MOBILE_SCALE = {
	"tablet": 0.9,  # 768px a menej
	"mobile": 0.8   # 480px a menej
}

static func get_font_config(font_type: String) -> Dictionary:
	"""Získa konfiguráciu fontu podľa typu"""
	if FONT_CONFIGS.has(font_type):
		return FONT_CONFIGS[font_type]
	else:
		return FONT_CONFIGS["ui_elements"]  # default

static func get_scaled_font_size(base_size: int, screen_width: int) -> int:
	"""Vypočíta škálovanú veľkosť fontu pre responzívnosť"""
	if screen_width <= 480:
		return int(base_size * MOBILE_SCALE["mobile"])
	elif screen_width <= 768:
		return int(base_size * MOBILE_SCALE["tablet"])
	else:
		return base_size

static func create_font_variation(font_type: String, custom_size: int = -1) -> FontVariation:
	"""Vytvorí font s danou konfiguráciou pomocou TTF súborov"""
	var config = get_font_config(font_type)

	# Získaj cestu k TTF súboru
	var font_path_key = config.get("font_path", "cinzel_regular")
	var ttf_path = FONT_PATHS.get(font_path_key, "")

	if ttf_path == "":
		print("CHYBA: Nenašiel sa TTF súbor pre: ", font_path_key)
		return null

	# Načítaj TTF súbor
	var base_font = load(ttf_path)
	if not base_font:
		print("CHYBA: Nemožno načítať TTF súbor: ", ttf_path)
		# Fallback na SystemFont
		base_font = SystemFont.new()
		var fallback_key = config.get("fallback", "clean_sans")
		if FALLBACK_FONTS.has(fallback_key):
			base_font.font_names = PackedStringArray(FALLBACK_FONTS[fallback_key])

	# Vytvoríme FontVariation pre pokročilé vlastnosti
	var font_variation = FontVariation.new()
	font_variation.base_font = base_font

	# Aplikujeme pokročilé vlastnosti podľa typu
	_apply_font_properties(font_variation, config)

	print("Vytvorený TTF font pre typ: ", font_type, " z: ", ttf_path)

	return font_variation

static func _apply_font_properties(font_variation: FontVariation, config: Dictionary):
	"""Aplikuje pokročilé vlastnosti fontu pomocou FontVariation"""

	# Font weight (embolden pre bold/medium efekt)
	var weight = config.get("weight", "regular")
	if weight == "bold":
		font_variation.variation_embolden = 0.8  # Bold efekt
	elif weight == "medium":
		font_variation.variation_embolden = 0.4  # Medium efekt
	# Pre "regular" neaplikujeme embolden

	# Italic štýl - len ak nie je už v TTF súbore
	if config.get("italic", false) and not config.get("font_path", "").contains("italic"):
		var transform = Transform2D()
		transform.x = Vector2(1.0, 0.0)
		transform.y = Vector2(0.2, 1.0)  # Skew pre italic efekt
		font_variation.variation_transform = transform

	# Extra spacing pre lepší vzhľad
	if config.get("shadow", false):
		# Pre fonty s tieňom pridáme trochu extra spacing
		font_variation.spacing_glyph = 1
		font_variation.spacing_space = 2

static func apply_font_style(label: Control, font_type: String, custom_size: int = -1):
	"""Aplikuje font štýl na Label alebo RichTextLabel"""
	var config = get_font_config(font_type)
	var font = create_font_variation(font_type, custom_size)

	if not font:
		print("CHYBA: Nemožno vytvoriť font pre typ: ", font_type)
		return

	var font_size = custom_size if custom_size > 0 else config.get("size", 16)
	var font_color = config.get("color", Color.WHITE)

	# Responzívne škálovanie
	var screen_width = label.get_viewport().get_visible_rect().size.x
	font_size = get_scaled_font_size(font_size, int(screen_width))

	print("Aplikujem font na ", label.get_class(), " - typ: ", font_type, ", veľkosť: ", font_size)

	# Aplikovanie fontu
	if label is Label:
		label.add_theme_font_override("font", font)
		label.add_theme_font_size_override("font_size", font_size)
		label.add_theme_color_override("font_color", font_color)

		# Outline a shadow len pre určité typy
		if font_type == "chapter_title" or font_type == "puzzle_text":
			label.add_theme_color_override("font_shadow_color", Color(0, 0, 0, 0.8))
			label.add_theme_constant_override("shadow_offset_x", 2)
			label.add_theme_constant_override("shadow_offset_y", 2)
			# Outline pre lepší vzhľad
			label.add_theme_color_override("font_outline_color", Color(0.2, 0.1, 0.05, 1))
			label.add_theme_constant_override("outline_size", 2)

	elif label is RichTextLabel:
		label.add_theme_font_override("normal_font", font)
		label.add_theme_font_size_override("normal_font_size", font_size)
		label.add_theme_color_override("default_color", font_color)

		# Outline pre RichTextLabel
		if font_type == "chapter_title" or font_type == "puzzle_text":
			label.add_theme_color_override("font_outline_color", Color(0.2, 0.1, 0.05, 1))
			label.add_theme_constant_override("outline_size", 1)

# Utility funkcie pre rôzne typy textov - podľa vášho návodu
static func apply_chapter_title_font(label: Control, custom_size: int = -1):
	"""Chapter názvy → Cinzel-Regular.ttf"""
	apply_font_style(label, "chapter_title", custom_size)

static func apply_menu_buttons_font(label: Control, custom_size: int = -1):
	"""Menu položky → Cinzel-Regular.ttf"""
	apply_font_style(label, "menu_buttons", custom_size)

static func apply_character_names_font(label: Control, custom_size: int = -1):
	"""Mená postav → Cinzel-Regular.ttf"""
	apply_font_style(label, "character_names", custom_size)

static func apply_character_dialogue_font(label: Control, custom_size: int = -1):
	"""Text rozhovorov → Cormorant-Regular.ttf"""
	apply_font_style(label, "character_dialogue", custom_size)

static func apply_narrator_font(label: Control, custom_size: int = -1):
	"""Rozprávač → Cormorant-Italic.ttf (fallback pre Crimson-Text.ttf)"""
	apply_font_style(label, "narrator_text", custom_size)

static func apply_ui_font(label: Control, custom_size: int = -1):
	"""Buttony, Nastavenia, HUD texty → Cinzel-Regular.ttf (fallback pre Montserrat-Medium.ttf)"""
	apply_font_style(label, "ui_elements", custom_size)

static func apply_puzzle_font(label: Control, custom_size: int = -1):
	"""Puzzle texty → Cinzel-Medium.ttf"""
	apply_font_style(label, "puzzle_text", custom_size)
