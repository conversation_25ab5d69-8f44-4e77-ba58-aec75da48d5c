extends Node

# AudioManager - Singleton pre správu audio systému v hre "Prekliate Dedičstvo"
# Spravuje background music, SFX, crossfade efekty a volume kontroly

# Audio players
var background_music: AudioStreamPlayer
var sfx_player: AudioStreamPlayer
var ui_player: AudioStreamPlayer
var voice_player: AudioStreamPlayer

# Audio Resources - priamo načítavanie MP3 súborov
var audio_tracks = {
	"main_menu": "res://audio/music/MainTheme.mp3",
	"storm_journey": "res://audio/music/2. Storm Journey - Búrlivá cesta.mp3",
	"forest_shadows": "res://audio/music/3. Forest of Shadows - Temný les.mp3",
	"castle_gates": "res://audio/music/4. Castle Gates - Brána hrôzy.mp3",
	"viktor_theme": "res://audio/music/5. <PERSON>'s Theme - Verný služobník.mp3",
	"library_secrets": "res://audio/music/6. Library of Secrets - Tajomná knižnica.mp3",
	"puzzle_theme": "res://audio/music/7. Puzzle Theme - Hádanky.mp3",
	"alchemy_lab": "res://audio/music/10. Alchemy Laboratory - Alchýmia.mp3",
	"descent_darkness": "res://audio/music/11. Descent into Darkness - Zostup do katakomb.mp3",
	"ancient_crypts": "res://audio/music/12. Ancient Crypts - Pradávne hrobky.mp3",
	"isabelle_awakening": "res://audio/music/13. Isabelle's Awakening - Prebudenie zla.mp3",
	"final_ritual": "res://audio/music/14. Final Ritual - Posledný rituál.mp3",
	"van_helsing_rescue": "res://audio/music/15. Van Helsing 15. Rescued - Záchrana mentora.mp3"
}

# Volume settings
var music_volume = 0.7
var sfx_volume = 0.8
var ui_volume = 0.6
var voice_volume = 1.0

# Current track tracking
var current_track = ""
var previous_track = ""

# Crossfade settings
var crossfade_time = 2.0
var is_crossfading = false

func _ready():
	print("🎵 AudioManager inicializovaný")
	setup_audio_players()
	setup_audio_buses()
	load_audio_settings()
	
	# Spustenie main menu hudby
	call_deferred("play_music", "main_menu")

func setup_audio_players():
	"""Vytvorí audio players ako child nodes"""
	background_music = AudioStreamPlayer.new()
	background_music.name = "BackgroundMusic"
	background_music.bus = "Music"
	add_child(background_music)
	
	sfx_player = AudioStreamPlayer.new()
	sfx_player.name = "SFXPlayer"
	sfx_player.bus = "SFX"
	add_child(sfx_player)
	
	ui_player = AudioStreamPlayer.new()
	ui_player.name = "UIPlayer"
	ui_player.bus = "UI"
	add_child(ui_player)
	
	voice_player = AudioStreamPlayer.new()
	voice_player.name = "VoicePlayer"
	voice_player.bus = "Voice"
	add_child(voice_player)
	
	print("✅ Audio players vytvorené")

func setup_audio_buses():
	"""Nastaví audio bus hierarchiu"""
	# AudioBus konfigurácia sa robí v Project Settings
	# Ale môžeme nastaviť volume levels programaticky
	
	# Nastavenie volume pre každý bus
	var music_bus_index = AudioServer.get_bus_index("Music")
	var sfx_bus_index = AudioServer.get_bus_index("SFX")
	var ui_bus_index = AudioServer.get_bus_index("UI")
	var voice_bus_index = AudioServer.get_bus_index("Voice")
	
	if music_bus_index != -1:
		AudioServer.set_bus_volume_db(music_bus_index, linear_to_db(music_volume))
	if sfx_bus_index != -1:
		AudioServer.set_bus_volume_db(sfx_bus_index, linear_to_db(sfx_volume))
	if ui_bus_index != -1:
		AudioServer.set_bus_volume_db(ui_bus_index, linear_to_db(ui_volume))
	if voice_bus_index != -1:
		AudioServer.set_bus_volume_db(voice_bus_index, linear_to_db(voice_volume))
	
	print("✅ Audio buses nastavené")

func load_audio_settings():
	"""Načíta uložené audio nastavenia"""
	# TODO: Implementovať načítanie z config súboru
	pass

func save_audio_settings():
	"""Uloží audio nastavenia"""
	# TODO: Implementovať uloženie do config súboru
	pass

# =============================================================================
# MUSIC CONTROL FUNKCIE
# =============================================================================

func play_music(track_name: String, fade_in: bool = true):
	"""Spustí hudbu s možnosťou crossfade"""
	if not audio_tracks.has(track_name):
		print("❌ Audio track neexistuje: ", track_name)
		return

	if current_track == track_name and background_music.playing:
		print("🎵 Track už hrá: ", track_name)
		return

	print("🎵 Spúšťam hudbu: ", track_name)
	previous_track = current_track
	current_track = track_name

	# Načítanie MP3 súboru
	var audio_stream = load(audio_tracks[track_name])
	if not audio_stream:
		print("❌ Nemožno načítať audio súbor: ", audio_tracks[track_name])
		return

	# Nastavenie loop pre background music
	if audio_stream is AudioStreamMP3:
		audio_stream.loop = true

	if fade_in and background_music.playing:
		crossfade_to_track(audio_stream)
	else:
		background_music.stream = audio_stream
		background_music.play()

func crossfade_to_track(new_track: AudioStream):
	"""Plynulý prechod medzi trackmi"""
	if is_crossfading:
		return

	is_crossfading = true
	var tween = create_tween()
	tween.set_parallel(true)

	# Fade out current track
	if background_music.playing:
		var current_volume = background_music.volume_db
		tween.tween_property(background_music, "volume_db", -30, crossfade_time)
		await tween.finished

	# Switch to new track
	background_music.stream = new_track
	background_music.volume_db = -30
	background_music.play()

	# Fade in new track
	var target_volume = linear_to_db(music_volume)
	tween.tween_property(background_music, "volume_db", target_volume, crossfade_time)
	await tween.finished

	is_crossfading = false

func stop_music(fade_out: bool = true):
	"""Zastaví hudbu s možnosťou fade out"""
	if not background_music.playing:
		return

	if fade_out:
		var tween = create_tween()
		tween.tween_property(background_music, "volume_db", -30, 1.5)
		await tween.finished
		background_music.stop()
		background_music.volume_db = linear_to_db(music_volume)
	else:
		background_music.stop()

	current_track = ""

func pause_music():
	"""Pozastaví hudbu"""
	background_music.stream_paused = true

func resume_music():
	"""Obnoví hudbu"""
	background_music.stream_paused = false

func get_current_track() -> String:
	"""Vráti názov aktuálne hrajúceho tracku"""
	return current_track

func get_previous_track() -> String:
	"""Vráti názov predchádzajúceho tracku"""
	return previous_track

# =============================================================================
# KAPITOLA-SPECIFIC TRIGGERY
# =============================================================================

# Kapitola 1: Príchod do zámku
func start_chapter_1():
	"""Spustí hudbu pre kapitolu 1 - búrlivá cesta"""
	play_music("storm_journey")

func enter_forest():
	"""Vstup do lesa - temná atmosféra"""
	play_music("forest_shadows")

# Kapitola 2: Stopy krvi
func start_chapter_2():
	"""Spustí hudbu pre kapitolu 2 - brána zámku"""
	play_music("castle_gates")

func meet_viktor():
	"""Stretnutie s Viktorom - jeho téma"""
	play_music("viktor_theme")

# Kapitola 3: Pátranie v zámku
func start_chapter_3():
	"""Spustí hudbu pre kapitolu 3 - knižnica tajomstiev"""
	play_music("library_secrets")

# Kapitola 4: Tajné krídlo
func start_chapter_4():
	"""Spustí hudbu pre kapitolu 4 - alchymistické laboratórium"""
	play_music("alchemy_lab")

# Kapitola 5: Krypty
func start_chapter_5():
	"""Spustí hudbu pre kapitolu 5 - zostup do temnoty"""
	play_music("descent_darkness")

func enter_crypts():
	"""Vstup do krypt - pradávne hrobky"""
	play_music("ancient_crypts")

# Kapitola 6: Konfrontácia
func start_chapter_6():
	"""Spustí hudbu pre kapitolu 6 - prebudenie Isabelle"""
	play_music("isabelle_awakening")

func final_battle():
	"""Finálny boj - posledný rituál"""
	play_music("final_ritual")

# Epilóg
func start_epilogue():
	"""Spustí hudbu pre epilóg - záchrana Van Helsinga"""
	play_music("van_helsing_rescue")

# Puzzle system
func start_puzzle():
	"""Spustí hudbu pre hlavolamy"""
	play_music("puzzle_theme")

func return_from_puzzle():
	"""Vráti sa k predchádzajúcej hudbe po dokončení puzzle"""
	if previous_track != "" and previous_track != "puzzle_theme":
		play_music(previous_track)
	else:
		# Fallback na základnú hudbu podľa kontextu
		play_music("library_secrets")

# =============================================================================
# VOLUME CONTROL FUNKCIE
# =============================================================================

func set_music_volume(value: float):
	"""Nastaví hlasitosť hudby (0.0 - 1.0)"""
	music_volume = clamp(value, 0.0, 1.0)
	var music_bus_index = AudioServer.get_bus_index("Music")
	if music_bus_index != -1:
		AudioServer.set_bus_volume_db(music_bus_index, linear_to_db(music_volume))
	save_audio_settings()

func set_sfx_volume(value: float):
	"""Nastaví hlasitosť SFX (0.0 - 1.0)"""
	sfx_volume = clamp(value, 0.0, 1.0)
	var sfx_bus_index = AudioServer.get_bus_index("SFX")
	if sfx_bus_index != -1:
		AudioServer.set_bus_volume_db(sfx_bus_index, linear_to_db(sfx_volume))
	save_audio_settings()

func set_ui_volume(value: float):
	"""Nastaví hlasitosť UI (0.0 - 1.0)"""
	ui_volume = clamp(value, 0.0, 1.0)
	var ui_bus_index = AudioServer.get_bus_index("UI")
	if ui_bus_index != -1:
		AudioServer.set_bus_volume_db(ui_bus_index, linear_to_db(ui_volume))
	save_audio_settings()

func set_voice_volume(value: float):
	"""Nastaví hlasitosť hlasu (0.0 - 1.0)"""
	voice_volume = clamp(value, 0.0, 1.0)
	var voice_bus_index = AudioServer.get_bus_index("Voice")
	if voice_bus_index != -1:
		AudioServer.set_bus_volume_db(voice_bus_index, linear_to_db(voice_volume))
	save_audio_settings()

func get_music_volume() -> float:
	"""Vráti aktuálnu hlasitosť hudby"""
	return music_volume

func get_sfx_volume() -> float:
	"""Vráti aktuálnu hlasitosť SFX"""
	return sfx_volume

func get_ui_volume() -> float:
	"""Vráti aktuálnu hlasitosť UI"""
	return ui_volume

func get_voice_volume() -> float:
	"""Vráti aktuálnu hlasitosť hlasu"""
	return voice_volume

# =============================================================================
# SFX FUNKCIE
# =============================================================================

func play_sfx(sound_path: String):
	"""Prehrá sound effect"""
	var audio_stream = load(sound_path)
	if audio_stream:
		sfx_player.stream = audio_stream
		sfx_player.play()

func play_ui_sound(sound_path: String):
	"""Prehrá UI sound"""
	var audio_stream = load(sound_path)
	if audio_stream:
		ui_player.stream = audio_stream
		ui_player.play()
